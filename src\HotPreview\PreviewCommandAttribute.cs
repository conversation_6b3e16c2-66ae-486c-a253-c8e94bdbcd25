namespace HotPreview;

/// <summary>
/// Designates this method as a command that can be executed from the DevTools UI or, soon, as an MCP tool command.
///
/// 
///
///
///
///
/// </summary>
/// <param name="displayName">Optional display name for the command, determining how it appears in navigation UI. Defaults to the method name if not specified.</param>
[AttributeUsage(AttributeTargets.Method)]
public sealed class PreviewCommandAttribute(string? displayName = null) : Attribute
{
    /// <summary>
    /// Optional display name for the command, determining how it appears in navigation UI.
    /// "/" delimiters can be used to indicate hierarchy.
    /// </summary>
    public string? DisplayName { get; } = displayName;
}
